
import { createContext, useContext, useState, useEffect, ReactNode } from "react";
import { useToast } from "@/components/ui/use-toast";
import { supabase } from "@/integrations/supabase/client";
import { useAuth } from "./AuthContext";

export type DayOfWeek = "monday" | "tuesday" | "wednesday" | "thursday" | "friday" | "saturday" | "sunday";
export type SocialPlatform = "linkedin" | "facebook" | "instagram";
export type ContentMode = "rss_with_ai" | "ai_only";
export type Language = "en" | "de" | "sk" | "cs" | "es" | "ru";

export type PostPreferencesType = {
  frequency: number; // posts per day
  daysOfWeek: DayOfWeek[];
  timeOfDay: string; // HH:MM format
  topics: string[];
  sentiment: "professional" | "optimistic" | "thoughtful" | "educational" | "inspirational" | "funny";
  makeComWebhookUrl?: string;
  platforms: SocialPlatform[];
  contentMode: ContentMode;
  rssFeeds: string[];
  language: Language;
  selectedLinkedInOrganization?: {
    id: string;
    organization_id: string;
    organization_name: string;
    organization_type: 'person' | 'org';
    vanity_name: string | null;
    logo_url: string | null;
  };
};

type PostPreferencesContextType = {
  preferences: PostPreferencesType;
  updatePreferences: (newPreferences: Partial<PostPreferencesType>) => void;
  savePreferences: () => Promise<void>;
  loading: boolean;
};

const defaultPreferences: PostPreferencesType = {
  frequency: 1,
  daysOfWeek: ["monday", "wednesday", "friday"],
  timeOfDay: "09:00",
  topics: [],
  sentiment: "professional",
  platforms: ["linkedin"],
  contentMode: "rss_with_ai",
  rssFeeds: [],
  language: "en",
};

const PostPreferencesContext = createContext<PostPreferencesContextType | undefined>(undefined);

export const PostPreferencesProvider = ({ children }: { children: ReactNode }) => {
  const [preferences, setPreferences] = useState<PostPreferencesType>(defaultPreferences);
  const [loading, setLoading] = useState(false);
  const { toast } = useToast();
  const { user, isAuthenticated } = useAuth();

  useEffect(() => {
    if (isAuthenticated && user) {
      loadUserPreferences();
    } else {
      // If not authenticated, load from localStorage as fallback
      const savedPreferences = localStorage.getItem("postPreferences");
      if (savedPreferences) {
        setPreferences(JSON.parse(savedPreferences));
      }
    }
  }, [isAuthenticated, user]);

  const loadUserPreferences = async () => {
    if (!user) return;
    
    try {
      setLoading(true);
      
      // First try to get agent preferences which might have the Make.com webhook URL and LinkedIn organization
      const { data: agentData, error: agentError } = await supabase
        .from('ai_agent_preferences')
        .select('preferences')
        .eq('user_id', user.id)
        .eq('agent_id', 'linkedin-thought-leader')
        .maybeSingle();

      // Get user post preferences from Supabase
      const { data, error } = await supabase
        .from('post_preferences')
        .select('*')
        .eq('user_id', user.id)
        .maybeSingle();

      if (error) throw error;

      let loadedPrefs: PostPreferencesType = {...defaultPreferences};

      if (data) {
        loadedPrefs = {
          frequency: data.frequency,
          daysOfWeek: data.days_of_week as DayOfWeek[],
          timeOfDay: data.time_of_day,
          topics: data.topics,
          sentiment: data.sentiment as "professional" | "optimistic" | "thoughtful" | "educational" | "inspirational" | "funny",
          platforms: data.platform as SocialPlatform[],
          contentMode: data.content_mode as ContentMode,
          rssFeeds: [],
          language: data.language as Language,
        };
      }

      // If we have agent preferences, try to get the Make.com webhook URL and LinkedIn organization
      if (!agentError && agentData && agentData.preferences) {
        // Fix TypeScript error - check that preferences is an object with makeComWebhookUrl property
        if (typeof agentData.preferences === 'object' &&
            agentData.preferences !== null &&
            !Array.isArray(agentData.preferences)) {

          if ('makeComWebhookUrl' in agentData.preferences) {
            loadedPrefs.makeComWebhookUrl = String(agentData.preferences.makeComWebhookUrl);
          }

          // Load LinkedIn organization selection
          if ('selectedLinkedInOrganization' in agentData.preferences) {
            loadedPrefs.selectedLinkedInOrganization = agentData.preferences.selectedLinkedInOrganization as any;
          }
        }
      }

      // Get RSS feeds (always load them, not just for RSS mode)
      const { data: rssFeeds, error: rssError } = await supabase
        .from('rss_feeds')
        .select('url')
        .eq('user_id', user.id);

      if (!rssError && rssFeeds) {
        loadedPrefs.rssFeeds = rssFeeds.map(feed => feed.url);
      }
      
      setPreferences(loadedPrefs);
      
      // Save to localStorage as a backup
      localStorage.setItem("postPreferences", JSON.stringify(loadedPrefs));
    } catch (error) {
      console.error("Error loading preferences:", error);
      toast({
        title: "Error loading preferences",
        description: "Could not load your preferences. Using defaults.",
        variant: "destructive",
      });
    } finally {
      setLoading(false);
    }
  };

  const updatePreferences = (newPreferences: Partial<PostPreferencesType>) => {
    setPreferences((prev) => {
      const updated = { ...prev, ...newPreferences };
      // Still save to localStorage as a backup
      localStorage.setItem("postPreferences", JSON.stringify(updated));
      return updated;
    });
  };

  const savePreferences = async () => {
    try {
      setLoading(true);
      
      if (!user) {
        // Just save to localStorage if not authenticated
        localStorage.setItem("postPreferences", JSON.stringify(preferences));
        toast({
          title: "Preferences saved locally",
          description: "Sign in to save your preferences to your account.",
        });
        return Promise.resolve();
      }
      
      // Save to Supabase post_preferences table
      const { error } = await supabase
        .from('post_preferences')
        .upsert({
          user_id: user.id,
          frequency: preferences.frequency,
          days_of_week: preferences.daysOfWeek,
          time_of_day: preferences.timeOfDay,
          topics: preferences.topics,
          sentiment: preferences.sentiment,
          platform: preferences.platforms,
          content_mode: preferences.contentMode,
          language: preferences.language
        }, { onConflict: 'user_id' });
      
      if (error) throw error;
      
      // If we have a Make.com webhook URL or LinkedIn organization, save it to the AI agent preferences
      if (preferences.makeComWebhookUrl || preferences.selectedLinkedInOrganization) {
        const agentPreferences: any = {};

        if (preferences.makeComWebhookUrl) {
          agentPreferences.makeComWebhookUrl = preferences.makeComWebhookUrl;
        }

        if (preferences.selectedLinkedInOrganization) {
          agentPreferences.selectedLinkedInOrganization = preferences.selectedLinkedInOrganization;
        }

        const { error: agentError } = await supabase
          .from('ai_agent_preferences')
          .upsert({
            user_id: user.id,
            agent_id: 'linkedin-thought-leader',
            preferences: agentPreferences,
            updated_at: new Date().toISOString()
          }, { onConflict: 'user_id,agent_id' });

        if (agentError) throw agentError;
      }
      
      // Handle RSS feeds - first delete existing
      if (preferences.contentMode === 'rss_with_ai' && preferences.rssFeeds.length > 0) {
        await supabase
          .from('rss_feeds')
          .delete()
          .eq('user_id', user.id);
          
        // Then insert new feeds
        const feedsToInsert = preferences.rssFeeds.map(url => ({
          user_id: user.id,
          url,
          title: null // Title will be fetched later
        }));
        
        const { error: rssError } = await supabase
          .from('rss_feeds')
          .insert(feedsToInsert);
          
        if (rssError) throw rssError;
      }
      
      toast({
        title: "Preferences saved",
        description: "Your posting preferences have been updated successfully.",
      });
      
      return Promise.resolve();
    } catch (error: any) {
      toast({
        title: "Error saving preferences",
        description: error.message || "Please try again later.",
        variant: "destructive",
      });
      console.error("Save preferences error:", error);
      return Promise.reject(error);
    } finally {
      setLoading(false);
    }
  };

  return (
    <PostPreferencesContext.Provider
      value={{
        preferences,
        updatePreferences,
        savePreferences,
        loading,
      }}
    >
      {children}
    </PostPreferencesContext.Provider>
  );
};

export const usePostPreferences = () => {
  const context = useContext(PostPreferencesContext);
  if (context === undefined) {
    throw new Error("usePostPreferences must be used within a PostPreferencesProvider");
  }
  return context;
};
